@echo off
title Sniper Elite 5 Offline Modifications
color 0A

echo.
echo  ███████╗███╗   ██╗██╗██████╗ ███████╗██████╗     ███████╗██╗     ██╗████████╗███████╗    ███████╗
echo  ██╔════╝████╗  ██║██║██╔══██╗██╔════╝██╔══██╗    ██╔════╝██║     ██║╚══██╔══╝██╔════╝    ██╔════╝
echo  ███████╗██╔██╗ ██║██║██████╔╝█████╗  ██████╔╝    █████╗  ██║     ██║   ██║   █████╗      ███████╗
echo  ╚════██║██║╚██╗██║██║██╔═══╝ ██╔══╝  ██╔══██╗    ██╔══╝  ██║     ██║   ██║   ██╔══╝      ╚════██║
echo  ███████║██║ ╚████║██║██║     ███████╗██║  ██║    ███████╗███████╗██║   ██║   ███████╗    ███████║
echo  ╚══════╝╚═╝  ╚═══╝╚═╝╚═╝     ╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝   ╚═╝   ╚══════╝    ╚══════╝
echo.
echo                                    OFFLINE MODIFICATIONS
echo                                  For Single-Player Use Only
echo.
echo ================================================================================================
echo.

echo [!] Running without administrator privileges (TEST MODE)

REM Change to the script's directory to ensure we're in the right location
cd /d "%~dp0"
echo [✓] Current directory: %CD%
echo.

REM Check for Python
echo [?] Checking for Python...
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [✗] ERROR: Python not found!
    echo [!] Please install Python from https://python.org
    echo.
    pause
    exit /b
)

for /f "tokens=*" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [✓] Found: %PYTHON_VERSION%
echo.

REM Check for game process
echo [?] Checking for Sniper Elite 5...
tasklist /FI "IMAGENAME eq Sniper5_dx12.exe" 2>NUL | find /I /N "Sniper5_dx12.exe" >NUL
if %errorLevel% == 0 (
    echo [✓] Sniper Elite 5 is running (DX12)
) else (
    tasklist /FI "IMAGENAME eq SniperElite5.exe" 2>NUL | find /I /N "SniperElite5.exe" >NUL
    if %errorLevel% == 0 (
        echo [✓] Sniper Elite 5 is running (Launcher)
    ) else (
        echo [!] WARNING: Sniper Elite 5 not detected
        echo [!] Make sure the game is running and you're in a level!
    )
)
echo.

REM Find and run the best implementation
set SCRIPT=
if exist "verified_working_implementation.py" (
    echo [✓] Found: verified_working_implementation.py (BEST)
    set SCRIPT=verified_working_implementation.py
    goto :script_found
)
if exist "real_offsets_implementation.py" (
    echo [✓] Found: real_offsets_implementation.py
    set SCRIPT=real_offsets_implementation.py
    goto :script_found
)
if exist "main.py" (
    echo [✓] Found: main.py (fallback)
    set SCRIPT=main.py
    goto :script_found
)

echo [✗] ERROR: No implementation files found!
echo [!] Make sure you're in the correct directory.
echo.
pause
exit /b

:script_found
echo.
echo ================================================================================================
echo                                        HOTKEYS
echo ================================================================================================
echo  F1 - God Mode (Invincibility)           [VERIFIED WORKING]
echo  F2 - Unlimited Ammo                     [VERIFIED WORKING] 
echo  F3 - Super Jump                         [FRAMEWORK READY]
echo  F4 - Aimbot                             [FRAMEWORK READY]
echo  F5 - Invisibility                       [VERIFIED WORKING]
echo  F12 - Exit Program
echo ================================================================================================
echo.
echo [!] IMPORTANT: Make sure you're IN A LEVEL, not in the main menu!
echo [!] For offline/single-player use only!
echo.
echo Starting modifications...
echo.

python %SCRIPT%

echo.
echo ================================================================================================
echo Program ended. Press any key to exit...
pause >nul
