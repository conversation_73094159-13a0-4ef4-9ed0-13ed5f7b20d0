#!/usr/bin/env python3
"""
Quick Address Test - Check if addresses become valid when in level
"""

import pymem
import time
from config import GameOffsets

def test_addresses_continuously():
    """Continuously test addresses to see when they become valid"""
    print("=" * 60)
    print("CONTINUOUS ADDRESS MONITOR")
    print("=" * 60)
    print("This will monitor addresses every 2 seconds.")
    print("Load into a level and watch for changes!")
    print("Press Ctrl+C to stop.")
    print("=" * 60)
    
    try:
        pm = pymem.Pymem(GameOffsets.PROCESS_NAME)
        print(f"✅ Attached to {GameOffsets.PROCESS_NAME}")
        print(f"Base Address: 0x{pm.base_address:X}")
        print()
        
        while True:
            print(f"[{time.strftime('%H:%M:%S')}] Testing addresses...")
            
            # Test key addresses
            addresses = [
                ("Health Base", GameOffsets.HEALTH_BASE_OFFSET),
                ("God Mode Base", GameOffsets.GOD_MODE_BASE_OFFSET),
                ("Player Base", GameOffsets.PLAYER_BASE_OFFSET),
                ("Weapon Base", GameOffsets.WEAPON_BASE_OFFSET),
                ("Ammo Base", GameOffsets.AMMO_BASE_OFFSET),
            ]
            
            valid_count = 0
            for name, offset in addresses:
                try:
                    address = pm.base_address + offset
                    value = pm.read_int(address)
                    if value and value != 0:
                        print(f"  ✅ {name:15}: 0x{value:X}")
                        valid_count += 1
                    else:
                        print(f"  ❌ {name:15}: Invalid/Null")
                except Exception as e:
                    print(f"  ❌ {name:15}: Error - {e}")
            
            print(f"  📊 Valid addresses: {valid_count}/5")
            
            if valid_count >= 4:
                print("  🎮 GOOD! Mods should work now!")
            elif valid_count >= 2:
                print("  ⚠️  PARTIAL - Some features may work")
            else:
                print("  ❌ NOT READY - Load into a level!")
            
            print("-" * 40)
            time.sleep(2)
            
    except pymem.exception.ProcessNotFound:
        print("❌ Game process not found!")
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped.")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_addresses_continuously()
