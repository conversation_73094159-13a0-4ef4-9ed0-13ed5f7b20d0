#!/usr/bin/env python3
"""
Sniper Elite 5 Game State Checker
=================================
This script helps diagnose why mods might not be working in-game.
"""

import pymem
import time
from config import GameOffsets

def check_game_state():
    """Check the current state of Sniper Elite 5 and memory addresses"""
    print("=" * 60)
    print("SNIPER ELITE 5 GAME STATE CHECKER")
    print("=" * 60)
    
    # Check if game process is running
    try:
        pm = pymem.Pymem(GameOffsets.PROCESS_NAME)
        print(f"✅ Game process found: {GameOffsets.PROCESS_NAME}")
        print(f"✅ Process ID: {pm.process_id}")
        print(f"✅ Base Address: 0x{pm.base_address:X}")
    except pymem.exception.ProcessNotFound:
        print(f"❌ Game process NOT found: {GameOffsets.PROCESS_NAME}")
        print("\n🔍 Checking alternative process names...")
        
        for alt_name in GameOffsets.ALTERNATIVE_PROCESS_NAMES:
            try:
                pm = pymem.Pymem(alt_name)
                print(f"✅ Found alternative process: {alt_name}")
                print(f"✅ Process ID: {pm.process_id}")
                print(f"✅ Base Address: 0x{pm.base_address:X}")
                break
            except pymem.exception.ProcessNotFound:
                print(f"❌ {alt_name} not found")
        else:
            print("\n❌ NO GAME PROCESS FOUND!")
            print("Make sure Sniper Elite 5 is running and you're in a level!")
            return False
    except Exception as e:
        print(f"❌ Error attaching to process: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("TESTING MEMORY ADDRESSES")
    print("=" * 60)
    
    # Test key memory addresses
    addresses_to_test = [
        ("Health Base", GameOffsets.HEALTH_BASE_OFFSET),
        ("God Mode Base", GameOffsets.GOD_MODE_BASE_OFFSET),
        ("Player Base", GameOffsets.PLAYER_BASE_OFFSET),
        ("Weapon Base", GameOffsets.WEAPON_BASE_OFFSET),
        ("Ammo Base", GameOffsets.AMMO_BASE_OFFSET),
    ]
    
    valid_addresses = 0
    total_addresses = len(addresses_to_test)
    
    for name, offset in addresses_to_test:
        try:
            address = pm.base_address + offset
            value = pm.read_int(address)
            if value and value != 0:
                print(f"✅ {name:15} (0x{offset:X}): 0x{value:X}")
                valid_addresses += 1
            else:
                print(f"⚠️  {name:15} (0x{offset:X}): Invalid/Null (0x{value:X})")
        except Exception as e:
            print(f"❌ {name:15} (0x{offset:X}): Error reading - {e}")
    
    print("\n" + "=" * 60)
    print("DIAGNOSIS")
    print("=" * 60)
    
    if valid_addresses == 0:
        print("❌ NO VALID ADDRESSES FOUND!")
        print("🔍 This usually means:")
        print("   1. You're in the main menu (not in a level)")
        print("   2. The game hasn't fully loaded yet")
        print("   3. You need to start a mission/level first")
        print("\n💡 SOLUTION:")
        print("   1. Start Sniper Elite 5")
        print("   2. Load into a level/mission (not just main menu)")
        print("   3. Wait for the level to fully load")
        print("   4. Then run the mod program")
        
    elif valid_addresses < total_addresses // 2:
        print("⚠️  PARTIAL ADDRESSES FOUND")
        print("🔍 Some addresses are working, but not all.")
        print("   This might work partially, but some features may not work.")
        print("\n💡 TRY:")
        print("   1. Make sure you're fully in a level (not loading screen)")
        print("   2. Try moving around in the game")
        print("   3. Wait a few seconds and try again")
        
    else:
        print("✅ GOOD! Most addresses are valid.")
        print("🎮 The mods should work now!")
        print("\n💡 HOTKEYS:")
        print("   F1 - God Mode")
        print("   F2 - Unlimited Ammo") 
        print("   F3 - Super Jump")
        print("   F4 - Aimbot")
        print("   F5 - Invisibility")
        print("   F12 - Exit")
    
    print(f"\n📊 Address Status: {valid_addresses}/{total_addresses} valid")
    
    # Close the process handle
    try:
        pm.close_process()
    except:
        pass
    
    return valid_addresses > 0

if __name__ == "__main__":
    check_game_state()
    print("\nPress Enter to exit...")
    input()
