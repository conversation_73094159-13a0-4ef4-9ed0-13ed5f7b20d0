@echo off
echo ================================================
echo Sniper Elite 5 Offline Modifications - Admin Launcher
echo ================================================
echo.

REM Check if already running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Already running as administrator.
    goto :run_program
) else (
    echo Requesting administrator privileges...
    echo Please click "Yes" when Windows asks for permission.
    echo.
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d \"%~dp0\" && \"%~f0\" admin' -Verb RunAs"
    goto :end
)

:run_program
echo ✓ Running with administrator privileges!
echo.
echo Current directory: %CD%
echo.

REM Check for Python
python --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ✗ ERROR: Python not found! Please install Python first.
    pause
    goto :end
)

echo ✓ Python found.
echo.

REM Try to run the best implementation first
if exist "verified_working_implementation.py" (
    echo ✓ Found verified_working_implementation.py
    echo.
    echo Starting Sniper Elite 5 modifications...
    echo Make sure you're in a game level (not main menu)!
    echo.
    echo Hotkeys will be:
    echo F1 - God Mode (Verified)
    echo F2 - Unlimited Ammo
    echo F3 - Super Jump
    echo F4 - Aimbot
    echo F5 - Invisibility (Verified)
    echo F12 - Exit
    echo.
    python verified_working_implementation.py
) else if exist "main.py" (
    echo ✓ Found main.py (fallback)
    echo.
    echo Starting Sniper Elite 5 modifications...
    echo Make sure you're in a game level (not main menu)!
    echo.
    python main.py
) else (
    echo ✗ ERROR: No implementation files found!
    echo Make sure you're in the correct directory.
    pause
)

:end
if not "%1"=="admin" (
    echo.
    echo Press any key to exit...
    pause >nul
)
