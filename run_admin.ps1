# PowerShell script to run Sniper Elite 5 mods as administrator
# This will automatically request admin privileges

Write-Host "Sniper Elite 5 Offline Modifications - Admin Launcher" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Host "Requesting administrator privileges..." -ForegroundColor Yellow
    
    # Relaunch as administrator
    Start-Process PowerShell -Verb RunAs -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`""
    exit
}

Write-Host "Running with administrator privileges!" -ForegroundColor Green
Write-Host ""

# Change to the script directory
Set-Location -Path $PSScriptRoot

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Cyan
Write-Host ""

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Python not found! Please install Python first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# Check if the verified implementation exists
if (Test-Path "verified_working_implementation.py") {
    Write-Host "Found verified_working_implementation.py" -ForegroundColor Green
    Write-Host ""
    Write-Host "Starting Sniper Elite 5 modifications..." -ForegroundColor Yellow
    Write-Host "Make sure you're in a game level (not main menu)!" -ForegroundColor Yellow
    Write-Host ""
    
    # Run the verified implementation
    python verified_working_implementation.py
} elseif (Test-Path "main.py") {
    Write-Host "Found main.py (fallback)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Starting Sniper Elite 5 modifications..." -ForegroundColor Yellow
    Write-Host "Make sure you're in a game level (not main menu)!" -ForegroundColor Yellow
    Write-Host ""
    
    # Run the main implementation
    python main.py
} else {
    Write-Host "ERROR: No implementation files found!" -ForegroundColor Red
    Write-Host "Make sure you're in the correct directory." -ForegroundColor Red
}

Write-Host ""
Write-Host "Press Enter to exit..." -ForegroundColor Gray
Read-Host
